//go:generate gen_sql -types=Source,AttemptStatus,AttemptStep

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/salaryestimation/salary_estimation_attempts.proto

package salaryestimation

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Source int32

const (
	Source_SOURCE_UNSPECIFIED Source = 0
	Source_SOURCE_AA          Source = 1
)

// Enum value maps for Source.
var (
	Source_name = map[int32]string{
		0: "SOURCE_UNSPECIFIED",
		1: "SOURCE_AA",
	}
	Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED": 0,
		"SOURCE_AA":          1,
	}
)

func (x Source) Enum() *Source {
	p := new(Source)
	*p = x
	return p
}

func (x Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Source) Descriptor() protoreflect.EnumDescriptor {
	return file_api_salaryestimation_salary_estimation_attempts_proto_enumTypes[0].Descriptor()
}

func (Source) Type() protoreflect.EnumType {
	return &file_api_salaryestimation_salary_estimation_attempts_proto_enumTypes[0]
}

func (x Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Source.Descriptor instead.
func (Source) EnumDescriptor() ([]byte, []int) {
	return file_api_salaryestimation_salary_estimation_attempts_proto_rawDescGZIP(), []int{0}
}

// Step enum for salary estimation attempts
type AttemptStep int32

const (
	AttemptStep_ATTEMPT_STEP_UNSPECIFIED        AttemptStep = 0
	AttemptStep_ATTEMPT_STEP_SALARY_ESTIMATION  AttemptStep = 1
	AttemptStep_ATTEMPT_STEP_ACCOUNT_CONNECTION AttemptStep = 2
	AttemptStep_ATTEMPT_STEP_CONSENT_COLLECTION AttemptStep = 3
	AttemptStep_ATTEMPT_STEP_DATA_SHARING       AttemptStep = 4
	AttemptStep_ATTEMPT_STEP_ANALYSIS           AttemptStep = 5
)

// Enum value maps for AttemptStep.
var (
	AttemptStep_name = map[int32]string{
		0: "ATTEMPT_STEP_UNSPECIFIED",
		1: "ATTEMPT_STEP_SALARY_ESTIMATION",
		2: "ATTEMPT_STEP_ACCOUNT_CONNECTION",
		3: "ATTEMPT_STEP_CONSENT_COLLECTION",
		4: "ATTEMPT_STEP_DATA_SHARING",
		5: "ATTEMPT_STEP_ANALYSIS",
	}
	AttemptStep_value = map[string]int32{
		"ATTEMPT_STEP_UNSPECIFIED":        0,
		"ATTEMPT_STEP_SALARY_ESTIMATION":  1,
		"ATTEMPT_STEP_ACCOUNT_CONNECTION": 2,
		"ATTEMPT_STEP_CONSENT_COLLECTION": 3,
		"ATTEMPT_STEP_DATA_SHARING":       4,
		"ATTEMPT_STEP_ANALYSIS":           5,
	}
)

func (x AttemptStep) Enum() *AttemptStep {
	p := new(AttemptStep)
	*p = x
	return p
}

func (x AttemptStep) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AttemptStep) Descriptor() protoreflect.EnumDescriptor {
	return file_api_salaryestimation_salary_estimation_attempts_proto_enumTypes[1].Descriptor()
}

func (AttemptStep) Type() protoreflect.EnumType {
	return &file_api_salaryestimation_salary_estimation_attempts_proto_enumTypes[1]
}

func (x AttemptStep) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AttemptStep.Descriptor instead.
func (AttemptStep) EnumDescriptor() ([]byte, []int) {
	return file_api_salaryestimation_salary_estimation_attempts_proto_rawDescGZIP(), []int{1}
}

// Status enum for salary estimation attempts
type AttemptStatus int32

const (
	AttemptStatus_ATTEMPT_STATUS_UNSPECIFIED AttemptStatus = 0
	AttemptStatus_ATTEMPT_STATUS_INITIATED   AttemptStatus = 1
	AttemptStatus_ATTEMPT_STATUS_PENDING     AttemptStatus = 2
	AttemptStatus_ATTEMPT_STATUS_IN_PROGRESS AttemptStatus = 3
	AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL  AttemptStatus = 4
	AttemptStatus_ATTEMPT_STATUS_FAILED      AttemptStatus = 5
	AttemptStatus_ATTEMPT_STATUS_SKIPPED     AttemptStatus = 6
)

// Enum value maps for AttemptStatus.
var (
	AttemptStatus_name = map[int32]string{
		0: "ATTEMPT_STATUS_UNSPECIFIED",
		1: "ATTEMPT_STATUS_INITIATED",
		2: "ATTEMPT_STATUS_PENDING",
		3: "ATTEMPT_STATUS_IN_PROGRESS",
		4: "ATTEMPT_STATUS_SUCCESSFUL",
		5: "ATTEMPT_STATUS_FAILED",
		6: "ATTEMPT_STATUS_SKIPPED",
	}
	AttemptStatus_value = map[string]int32{
		"ATTEMPT_STATUS_UNSPECIFIED": 0,
		"ATTEMPT_STATUS_INITIATED":   1,
		"ATTEMPT_STATUS_PENDING":     2,
		"ATTEMPT_STATUS_IN_PROGRESS": 3,
		"ATTEMPT_STATUS_SUCCESSFUL":  4,
		"ATTEMPT_STATUS_FAILED":      5,
		"ATTEMPT_STATUS_SKIPPED":     6,
	}
)

func (x AttemptStatus) Enum() *AttemptStatus {
	p := new(AttemptStatus)
	*p = x
	return p
}

func (x AttemptStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AttemptStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_salaryestimation_salary_estimation_attempts_proto_enumTypes[2].Descriptor()
}

func (AttemptStatus) Type() protoreflect.EnumType {
	return &file_api_salaryestimation_salary_estimation_attempts_proto_enumTypes[2]
}

func (x AttemptStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AttemptStatus.Descriptor instead.
func (AttemptStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_salaryestimation_salary_estimation_attempts_proto_rawDescGZIP(), []int{2}
}

// Field mask for SalaryEstimationAttempt updates
type SalaryEstimationAttemptFieldMask int32

const (
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UNSPECIFIED   SalaryEstimationAttemptFieldMask = 0
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_SOURCE        SalaryEstimationAttemptFieldMask = 1
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_CLIENT_PARAMS SalaryEstimationAttemptFieldMask = 2
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STEP          SalaryEstimationAttemptFieldMask = 3
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS        SalaryEstimationAttemptFieldMask = 4
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_ATTEMPT_INFO  SalaryEstimationAttemptFieldMask = 5
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UPDATED_AT    SalaryEstimationAttemptFieldMask = 6
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_DELETED_AT    SalaryEstimationAttemptFieldMask = 7
)

// Enum value maps for SalaryEstimationAttemptFieldMask.
var (
	SalaryEstimationAttemptFieldMask_name = map[int32]string{
		0: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UNSPECIFIED",
		1: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_SOURCE",
		2: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_CLIENT_PARAMS",
		3: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STEP",
		4: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS",
		5: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_ATTEMPT_INFO",
		6: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UPDATED_AT",
		7: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_DELETED_AT",
	}
	SalaryEstimationAttemptFieldMask_value = map[string]int32{
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UNSPECIFIED":   0,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_SOURCE":        1,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_CLIENT_PARAMS": 2,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STEP":          3,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS":        4,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_ATTEMPT_INFO":  5,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UPDATED_AT":    6,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_DELETED_AT":    7,
	}
)

func (x SalaryEstimationAttemptFieldMask) Enum() *SalaryEstimationAttemptFieldMask {
	p := new(SalaryEstimationAttemptFieldMask)
	*p = x
	return p
}

func (x SalaryEstimationAttemptFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SalaryEstimationAttemptFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_salaryestimation_salary_estimation_attempts_proto_enumTypes[3].Descriptor()
}

func (SalaryEstimationAttemptFieldMask) Type() protoreflect.EnumType {
	return &file_api_salaryestimation_salary_estimation_attempts_proto_enumTypes[3]
}

func (x SalaryEstimationAttemptFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SalaryEstimationAttemptFieldMask.Descriptor instead.
func (SalaryEstimationAttemptFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_salaryestimation_salary_estimation_attempts_proto_rawDescGZIP(), []int{3}
}

// AttemptInfo contains metadata about the salary estimation attempt
type AttemptInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Connected account IDs involved in the attempt
	ConnectedAccountIds []string `protobuf:"bytes,1,rep,name=connected_account_ids,json=connectedAccountIds,proto3" json:"connected_account_ids,omitempty"`
	// Perpetual consent ID for the attempt
	PerpetualConsentId string `protobuf:"bytes,2,opt,name=perpetual_consent_id,json=perpetualConsentId,proto3" json:"perpetual_consent_id,omitempty"`
	// Latest valid one-time consent ID
	LatestValidOneTimeConsentId string `protobuf:"bytes,3,opt,name=latest_valid_one_time_consent_id,json=latestValidOneTimeConsentId,proto3" json:"latest_valid_one_time_consent_id,omitempty"`
	// Data sharing client request ID
	DataSharingClientReqId string `protobuf:"bytes,4,opt,name=data_sharing_client_req_id,json=dataSharingClientReqId,proto3" json:"data_sharing_client_req_id,omitempty"`
	// Analysis client request ID
	AnalysisClientReqId string `protobuf:"bytes,5,opt,name=analysis_client_req_id,json=analysisClientReqId,proto3" json:"analysis_client_req_id,omitempty"`
}

func (x *AttemptInfo) Reset() {
	*x = AttemptInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryestimation_salary_estimation_attempts_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttemptInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttemptInfo) ProtoMessage() {}

func (x *AttemptInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryestimation_salary_estimation_attempts_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttemptInfo.ProtoReflect.Descriptor instead.
func (*AttemptInfo) Descriptor() ([]byte, []int) {
	return file_api_salaryestimation_salary_estimation_attempts_proto_rawDescGZIP(), []int{0}
}

func (x *AttemptInfo) GetConnectedAccountIds() []string {
	if x != nil {
		return x.ConnectedAccountIds
	}
	return nil
}

func (x *AttemptInfo) GetPerpetualConsentId() string {
	if x != nil {
		return x.PerpetualConsentId
	}
	return ""
}

func (x *AttemptInfo) GetLatestValidOneTimeConsentId() string {
	if x != nil {
		return x.LatestValidOneTimeConsentId
	}
	return ""
}

func (x *AttemptInfo) GetDataSharingClientReqId() string {
	if x != nil {
		return x.DataSharingClientReqId
	}
	return ""
}

func (x *AttemptInfo) GetAnalysisClientReqId() string {
	if x != nil {
		return x.AnalysisClientReqId
	}
	return ""
}

type ClientParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ClientParams) Reset() {
	*x = ClientParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryestimation_salary_estimation_attempts_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientParams) ProtoMessage() {}

func (x *ClientParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryestimation_salary_estimation_attempts_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientParams.ProtoReflect.Descriptor instead.
func (*ClientParams) Descriptor() ([]byte, []int) {
	return file_api_salaryestimation_salary_estimation_attempts_proto_rawDescGZIP(), []int{1}
}

// SalaryEstimationAttempt represents a salary estimation attempt record
type SalaryEstimationAttempt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for the salary estimation attempt
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Client request ID - unique identifier provided by the client
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// Source of the salary estimation attempt
	Source Source `protobuf:"varint,3,opt,name=source,proto3,enum=salaryestimation.Source" json:"source,omitempty"`
	// Actor ID associated with the attempt
	ActorId string `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Client parameters for the attempt
	ClientParams *ClientParams `protobuf:"bytes,5,opt,name=client_params,json=clientParams,proto3" json:"client_params,omitempty"`
	// Current step of the attempt
	Step AttemptStep `protobuf:"varint,6,opt,name=step,proto3,enum=salaryestimation.AttemptStep" json:"step,omitempty"`
	// Current status of the attempt
	Status AttemptStatus `protobuf:"varint,7,opt,name=status,proto3,enum=salaryestimation.AttemptStatus" json:"status,omitempty"`
	// Attempt metadata and information
	AttemptInfo *AttemptInfo `protobuf:"bytes,8,opt,name=attempt_info,json=attemptInfo,proto3" json:"attempt_info,omitempty"`
	// Timestamp when the record was created
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Timestamp when the record was last updated
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Timestamp when the record was deleted (soft delete)
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *SalaryEstimationAttempt) Reset() {
	*x = SalaryEstimationAttempt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryestimation_salary_estimation_attempts_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SalaryEstimationAttempt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SalaryEstimationAttempt) ProtoMessage() {}

func (x *SalaryEstimationAttempt) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryestimation_salary_estimation_attempts_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SalaryEstimationAttempt.ProtoReflect.Descriptor instead.
func (*SalaryEstimationAttempt) Descriptor() ([]byte, []int) {
	return file_api_salaryestimation_salary_estimation_attempts_proto_rawDescGZIP(), []int{2}
}

func (x *SalaryEstimationAttempt) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SalaryEstimationAttempt) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *SalaryEstimationAttempt) GetSource() Source {
	if x != nil {
		return x.Source
	}
	return Source_SOURCE_UNSPECIFIED
}

func (x *SalaryEstimationAttempt) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *SalaryEstimationAttempt) GetClientParams() *ClientParams {
	if x != nil {
		return x.ClientParams
	}
	return nil
}

func (x *SalaryEstimationAttempt) GetStep() AttemptStep {
	if x != nil {
		return x.Step
	}
	return AttemptStep_ATTEMPT_STEP_UNSPECIFIED
}

func (x *SalaryEstimationAttempt) GetStatus() AttemptStatus {
	if x != nil {
		return x.Status
	}
	return AttemptStatus_ATTEMPT_STATUS_UNSPECIFIED
}

func (x *SalaryEstimationAttempt) GetAttemptInfo() *AttemptInfo {
	if x != nil {
		return x.AttemptInfo
	}
	return nil
}

func (x *SalaryEstimationAttempt) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SalaryEstimationAttempt) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *SalaryEstimationAttempt) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_salaryestimation_salary_estimation_attempts_proto protoreflect.FileDescriptor

var file_api_salaryestimation_salary_estimation_attempts_proto_rawDesc = []byte{
	0x0a, 0x35, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65,
	0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xab, 0x02, 0x0a, 0x0b, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x13, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x65, 0x72, 0x70, 0x65,
	0x74, 0x75, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x65, 0x72, 0x70, 0x65, 0x74, 0x75, 0x61, 0x6c,
	0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x20, 0x6c, 0x61, 0x74,
	0x65, 0x73, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x1b, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x4f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x3a, 0x0a, 0x1a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x64, 0x61, 0x74, 0x61, 0x53, 0x68, 0x61, 0x72, 0x69, 0x6e,
	0x67, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x16,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49,
	0x64, 0x22, 0x0e, 0x0a, 0x0c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x22, 0xd0, 0x04, 0x0a, 0x17, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x45, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2b, 0x0a,
	0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x73, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x43, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79,
	0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x31, 0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x53, 0x74,
	0x65, 0x70, 0x52, 0x04, 0x73, 0x74, 0x65, 0x70, 0x12, 0x37, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72,
	0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x40, 0x0a, 0x0c, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79,
	0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x2a, 0x2f, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16,
	0x0a, 0x12, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45,
	0x5f, 0x41, 0x41, 0x10, 0x01, 0x2a, 0xd3, 0x01, 0x0a, 0x0b, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x53, 0x74, 0x65, 0x70, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54,
	0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x54, 0x54, 0x45, 0x4d,
	0x50, 0x54, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f,
	0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x43, 0x4f, 0x4e,
	0x53, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x03, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x45,
	0x50, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x04,
	0x12, 0x19, 0x0a, 0x15, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x10, 0x05, 0x2a, 0xdf, 0x01, 0x0a, 0x0d,
	0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a,
	0x1a, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a,
	0x18, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x41,
	0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x54, 0x54, 0x45, 0x4d,
	0x50, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x54, 0x54, 0x45, 0x4d,
	0x50, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x46, 0x55, 0x4c, 0x10, 0x04, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x05, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x53, 0x4b, 0x49, 0x50, 0x50, 0x45, 0x44, 0x10, 0x06, 0x2a, 0xc2, 0x03,
	0x0a, 0x20, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x12, 0x34, 0x0a, 0x30, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54,
	0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2f, 0x0a, 0x2b, 0x53, 0x41, 0x4c, 0x41,
	0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54,
	0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x10, 0x01, 0x12, 0x36, 0x0a, 0x32, 0x53, 0x41, 0x4c,
	0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41,
	0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x53, 0x10,
	0x02, 0x12, 0x2d, 0x0a, 0x29, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49,
	0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x10, 0x03,
	0x12, 0x2f, 0x0a, 0x2b, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10,
	0x04, 0x12, 0x35, 0x0a, 0x31, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49,
	0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50,
	0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x05, 0x12, 0x33, 0x0a, 0x2f, 0x53, 0x41, 0x4c, 0x41,
	0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54,
	0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x06, 0x12, 0x33, 0x0a,
	0x2f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54,
	0x10, 0x07, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_salaryestimation_salary_estimation_attempts_proto_rawDescOnce sync.Once
	file_api_salaryestimation_salary_estimation_attempts_proto_rawDescData = file_api_salaryestimation_salary_estimation_attempts_proto_rawDesc
)

func file_api_salaryestimation_salary_estimation_attempts_proto_rawDescGZIP() []byte {
	file_api_salaryestimation_salary_estimation_attempts_proto_rawDescOnce.Do(func() {
		file_api_salaryestimation_salary_estimation_attempts_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_salaryestimation_salary_estimation_attempts_proto_rawDescData)
	})
	return file_api_salaryestimation_salary_estimation_attempts_proto_rawDescData
}

var file_api_salaryestimation_salary_estimation_attempts_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_salaryestimation_salary_estimation_attempts_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_salaryestimation_salary_estimation_attempts_proto_goTypes = []interface{}{
	(Source)(0),                           // 0: salaryestimation.Source
	(AttemptStep)(0),                      // 1: salaryestimation.AttemptStep
	(AttemptStatus)(0),                    // 2: salaryestimation.AttemptStatus
	(SalaryEstimationAttemptFieldMask)(0), // 3: salaryestimation.SalaryEstimationAttemptFieldMask
	(*AttemptInfo)(nil),                   // 4: salaryestimation.AttemptInfo
	(*ClientParams)(nil),                  // 5: salaryestimation.ClientParams
	(*SalaryEstimationAttempt)(nil),       // 6: salaryestimation.SalaryEstimationAttempt
	(*timestamppb.Timestamp)(nil),         // 7: google.protobuf.Timestamp
}
var file_api_salaryestimation_salary_estimation_attempts_proto_depIdxs = []int32{
	0, // 0: salaryestimation.SalaryEstimationAttempt.source:type_name -> salaryestimation.Source
	5, // 1: salaryestimation.SalaryEstimationAttempt.client_params:type_name -> salaryestimation.ClientParams
	1, // 2: salaryestimation.SalaryEstimationAttempt.step:type_name -> salaryestimation.AttemptStep
	2, // 3: salaryestimation.SalaryEstimationAttempt.status:type_name -> salaryestimation.AttemptStatus
	4, // 4: salaryestimation.SalaryEstimationAttempt.attempt_info:type_name -> salaryestimation.AttemptInfo
	7, // 5: salaryestimation.SalaryEstimationAttempt.created_at:type_name -> google.protobuf.Timestamp
	7, // 6: salaryestimation.SalaryEstimationAttempt.updated_at:type_name -> google.protobuf.Timestamp
	7, // 7: salaryestimation.SalaryEstimationAttempt.deleted_at:type_name -> google.protobuf.Timestamp
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_salaryestimation_salary_estimation_attempts_proto_init() }
func file_api_salaryestimation_salary_estimation_attempts_proto_init() {
	if File_api_salaryestimation_salary_estimation_attempts_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_salaryestimation_salary_estimation_attempts_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttemptInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_salaryestimation_salary_estimation_attempts_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_salaryestimation_salary_estimation_attempts_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SalaryEstimationAttempt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_salaryestimation_salary_estimation_attempts_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_salaryestimation_salary_estimation_attempts_proto_goTypes,
		DependencyIndexes: file_api_salaryestimation_salary_estimation_attempts_proto_depIdxs,
		EnumInfos:         file_api_salaryestimation_salary_estimation_attempts_proto_enumTypes,
		MessageInfos:      file_api_salaryestimation_salary_estimation_attempts_proto_msgTypes,
	}.Build()
	File_api_salaryestimation_salary_estimation_attempts_proto = out.File
	file_api_salaryestimation_salary_estimation_attempts_proto_rawDesc = nil
	file_api_salaryestimation_salary_estimation_attempts_proto_goTypes = nil
	file_api_salaryestimation_salary_estimation_attempts_proto_depIdxs = nil
}
